import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor

from Agents.ImproveInfoAgent import ImproveInfoAgent
from Utils.logs.LoggingConfig import logger


class AsyncImproveInfoAgent:
    """异步ImproveInfoAgent包装器，避免LLM调用阻塞其他接口"""
    
    def __init__(self):
        self._improve_agent = ImproveInfoAgent()

    async def evaluate_improve_info_async(self, evaluate: str):
        """异步版本的evaluateImproveInfo"""
        # 原方法已经是异步的，直接调用
        return await self._improve_agent.evaluateImproveInfo(evaluate)

    def cleanup(self):
        """清理资源"""
        try:
            logger.info("AsyncImproveInfoAgent资源清理完成")
        except Exception as e:
            logger.error(f"清理AsyncImproveInfoAgent资源时出错: {e}")


# 创建全局异步代理实例
async_improve_info_agent = AsyncImproveInfoAgent()
