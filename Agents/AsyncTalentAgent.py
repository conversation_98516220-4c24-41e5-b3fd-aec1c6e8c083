import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from typing import Optional

from Agents.TalentAgent import TalentAgent
from Agents.TalentAgentProcessing import TalentAgentProcessing
from Models.agent.ResumeInfo import ResumeInfo
from Models.dto.TaskInfoDto import TaskInfoDto
from Utils.logs.LoggingConfig import logger


class AsyncTalentAgent:
    """异步TalentAgent包装器，避免LLM调用阻塞其他接口"""

    def __init__(self):
        self._talent_agent = TalentAgent()
        self._talent_processor = TalentAgentProcessing()
        # 创建线程池用于同步方法
        self._executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="TalentAgent-Worker")

    async def chat_for_answer_async(self, content: str, job_id: int = None):
        """异步版本的chat_for_answer"""
        # 将整个方法放到线程池中执行，避免阻塞事件循环
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._sync_chat_for_answer,
            content,
            job_id
        )
    
    def _sync_chat_for_answer(self, content: str, job_id: int = None):
        """同步版本的chat_for_answer，在线程池中执行"""
        try:
            task_dto = self._talent_agent.getPostingInfo(job_id)
            if task_dto is None:
                logger.error(f"任务id:{job_id},该任务信息不存在")
                return 0

            from Models.dto.TaskInfoDto import TaskInfoDto
            taskInfo = TaskInfoDto.from_dict(task_dto)
            # 使用TalentAgentProcessing进行解析
            talent_info = self._talent_agent._formatting(content, taskInfo.screeningConditions, True)
            if talent_info is None:
                logger.error(f"简历解析失败，任务id:{job_id}")
                return 0
            
            # 过滤校验
            code = self._talent_agent.condition_filter(talent_info, job_id, taskInfo)
            if code == 0:
                return code

            # 更新值
            talent_info.jobId = job_id
            if talent_info.sex != "男" and talent_info.sex != "女":
                talent_info.sex = ""
            
            # 启动另一个线程去处理保存
            import threading
            threading.Thread(target=self._talent_agent._TalentAgent__saveConversationMemory, args=(talent_info,), daemon=True).start()
            
            return code
        except Exception as e:
            logger.error(f"Error in sync_chat_for_answer: {str(e)}")
            return 0

    async def formatting_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的简历格式化"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_agent._formatting,
            content,
            condition,
            boss_flag
        )

    async def formatting_processing_async(self, content: str, condition: str = None, boss_flag: bool = False) -> Optional[ResumeInfo]:
        """异步版本的TalentAgentProcessing格式化"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_processor._formatting,
            content,
            condition,
            boss_flag
        )

    async def calculate_total_score_async(self, talent_info: ResumeInfo, task_info: TaskInfoDto) -> float:
        """异步版本的总分计算"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self._executor,
            self._talent_agent.calculate_total_score,
            talent_info,
            task_info
        )
    
    # 异步数据库操作方法
    async def getPostingInfo_async(self, job_id: int):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, self._talent_agent.getPostingInfo, job_id)
    
    async def getAnalyseTaskInfo_async(self, task_id: int):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, self._talent_agent.getAnalyseTaskInfo, task_id)
    
    async def updateFileStatus_async(self, file_id: int, status: int):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, self._talent_agent.updateFileStatus, file_id, status)
    
    async def saveAnalyseTaskInfo_async(self, talent_info: ResumeInfo):
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self._executor, self._talent_agent.saveAnalyseTaskInfo, talent_info)
    
    # 同步方法的直接代理（保持向后兼容）
    def getPostingInfo(self, job_id: int):
        return self._talent_agent.getPostingInfo(job_id)
    
    def getAnalyseTaskInfo(self, task_id: int):
        return self._talent_agent.getAnalyseTaskInfo(task_id)
    
    def updateFileStatus(self, file_id: int, status: int):
        return self._talent_agent.updateFileStatus(file_id, status)
    
    def saveAnalyseTaskInfo(self, talent_info: ResumeInfo):
        return self._talent_agent.saveAnalyseTaskInfo(talent_info)

    def condition_filter(self, talent_info: ResumeInfo, job_id: int = None, requirements: TaskInfoDto = None):
        return self._talent_agent.condition_filter(talent_info, job_id, requirements)
    
    def calculate_total_score(self, talent_info: ResumeInfo, task_info: TaskInfoDto) -> float:
        return self._talent_agent.calculate_total_score(talent_info, task_info)
    
    def cleanup(self):
        """清理资源"""
        try:
            self._executor.shutdown(wait=True)
            logger.info("AsyncTalentAgent线程池已关闭")
        except Exception as e:
            logger.error(f"清理AsyncTalentAgent资源时出错: {e}")


# 创建全局异步代理实例
async_talent_agent = AsyncTalentAgent()
